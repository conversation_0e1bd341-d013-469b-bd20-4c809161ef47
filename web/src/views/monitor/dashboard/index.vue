<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { NGrid, NGridItem, NButton, NSpace, NSpin, NAlert, NTag } from 'naive-ui';
import AppHealthOverview from './modules/app-health-overview.vue';
import BusinessStats from './modules/business-stats.vue';
import PerformanceCharts from './modules/performance-charts.vue';
import ErrorAnalysis from './modules/error-analysis.vue';
import StrmTaskMonitor from './modules/strm-task-monitor.vue';
import SystemResources from './modules/system-resources.vue';
import { useRealtimeData } from './composables/useRealtimeData';

// 监控数据状态
const loading = ref(false);
const error = ref<string | null>(null);

// 组件引用
const appHealthRef = ref();
const businessStatsRef = ref();
const performanceChartsRef = ref();
const errorAnalysisRef = ref();
const strmTaskMonitorRef = ref();
const systemResourcesRef = ref();

// SSE实时数据
const {
  isConnected,
  isConnecting,
  realtimeData,
  error: sseError,
  lastUpdateTime,
  connect,
  disconnect,
  reconnect,
  getConnectionStatus
} = useRealtimeData();

// 是否启用实时模式
const realtimeMode = ref(true);

// 自动刷新定时器（作为SSE的备用方案）
let refreshTimer: NodeJS.Timeout | null = null;

onMounted(async () => {
  try {
    if (realtimeMode.value) {
      // 启动SSE实时连接
      connect();
    } else {
      // 启动定时器轮询（备用方案）
      startAutoRefresh();
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : '初始化监控页面失败';
  }
});

onUnmounted(() => {
  // 清理资源
  disconnect();
  if (refreshTimer) {
    clearInterval(refreshTimer);
    refreshTimer = null;
  }
});

// 监听SSE数据变化，更新组件
watch(realtimeData, (newData) => {
  if (newData && !newData.error) {
    // 更新各个组件的数据
    if (appHealthRef.value?.updateData) {
      appHealthRef.value.updateData(newData.health);
    }
    if (businessStatsRef.value?.updateData) {
      businessStatsRef.value.updateData(newData.business);
    }
    if (performanceChartsRef.value?.updateData) {
      performanceChartsRef.value.updateData(newData.performance);
    }
  }
});

// 监听SSE错误
watch(sseError, (newError) => {
  if (newError) {
    error.value = `实时连接错误: ${newError}`;
  }
});

function startAutoRefresh() {
  // 每30秒自动刷新一次（备用方案）
  refreshTimer = setInterval(() => {
    handleRefresh();
  }, 30000);
}

function handleRefresh() {
  // 触发所有组件刷新
  if (appHealthRef.value?.loadHealthData) {
    appHealthRef.value.loadHealthData();
  }
  if (businessStatsRef.value?.loadBusinessData) {
    businessStatsRef.value.loadBusinessData();
  }
  if (performanceChartsRef.value?.loadPerformanceData) {
    performanceChartsRef.value.loadPerformanceData();
  }
  if (errorAnalysisRef.value?.loadErrorData) {
    errorAnalysisRef.value.loadErrorData();
  }
  if (strmTaskMonitorRef.value?.loadTaskStats) {
    strmTaskMonitorRef.value.loadTaskStats();
  }
  if (systemResourcesRef.value?.loadSystemData) {
    systemResourcesRef.value.loadSystemData();
  }
}

function toggleRealtimeMode() {
  realtimeMode.value = !realtimeMode.value;

  if (realtimeMode.value) {
    // 切换到实时模式
    if (refreshTimer) {
      clearInterval(refreshTimer);
      refreshTimer = null;
    }
    connect();
  } else {
    // 切换到轮询模式
    disconnect();
    startAutoRefresh();
    handleRefresh(); // 立即刷新一次
  }
}

function handleManualRefresh() {
  if (realtimeMode.value) {
    // 实时模式下重连SSE
    reconnect();
  } else {
    // 轮询模式下手动刷新
    handleRefresh();
  }
}
</script>

<template>
  <div class="monitor-dashboard">
    <!-- 页面头部 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">系统监控</h1>
        <div class="flex items-center gap-4 mt-1">
          <p class="text-gray-600">实时监控应用健康状态、性能指标和业务数据</p>

          <!-- 连接状态指示器 -->
          <div class="flex items-center gap-2">
            <NTag
              :type="isConnected ? 'success' : isConnecting ? 'warning' : 'error'"
              size="small"
            >
              {{ getConnectionStatus() }}
            </NTag>

            <span v-if="lastUpdateTime" class="text-xs text-gray-500">
              最后更新: {{ new Date(lastUpdateTime).toLocaleTimeString() }}
            </span>
          </div>
        </div>
      </div>

      <NSpace>
        <NButton
          @click="toggleRealtimeMode"
          :type="realtimeMode ? 'primary' : 'default'"
          size="small"
        >
          {{ realtimeMode ? '实时模式' : '轮询模式' }}
        </NButton>

        <NButton
          @click="handleManualRefresh"
          :loading="loading || isConnecting"
        >
          {{ realtimeMode ? '重连' : '刷新数据' }}
        </NButton>
      </NSpace>
    </div>

    <NSpin :show="loading">
      <div v-if="error" class="mb-4">
        <NAlert type="error" :title="error" closable @close="error = null" />
      </div>

      <NGrid :cols="24" :x-gap="16" :y-gap="16">
        <!-- 应用健康状态概览 -->
        <NGridItem :span="24">
          <AppHealthOverview ref="appHealthRef" />
        </NGridItem>

        <!-- 业务统计 -->
        <NGridItem :span="12">
          <BusinessStats ref="businessStatsRef" />
        </NGridItem>

        <!-- 性能监控 -->
        <NGridItem :span="12">
          <PerformanceCharts ref="performanceChartsRef" />
        </NGridItem>

        <!-- STRM任务监控 -->
        <NGridItem :span="12">
          <StrmTaskMonitor ref="strmTaskMonitorRef" />
        </NGridItem>

        <!-- 错误分析 -->
        <NGridItem :span="12">
          <ErrorAnalysis ref="errorAnalysisRef" />
        </NGridItem>

        <!-- 系统资源监控 -->
        <NGridItem :span="24">
          <SystemResources ref="systemResourcesRef" />
        </NGridItem>

        <!-- 用户活动监控 -->
        <!-- <NGridItem :span="12">
          <UserActivityMonitor />
        </NGridItem> -->
      </NGrid>
    </NSpin>

    <!-- 自动刷新提示 -->
    <div class="fixed bottom-4 right-4 text-xs text-gray-500 bg-white px-3 py-2 rounded-lg shadow-sm border">
      自动刷新: 每30秒
    </div>
  </div>
</template>

<style scoped>
.monitor-dashboard {
  padding: 16px;
  min-height: calc(100vh - 64px);
}

.h-full {
  height: 100%;
}
</style>
