import { request } from '../request';

/**
 * 监控模块API服务
 */

/**
 * 获取应用健康状态
 */
export function fetchAppHealth() {
  return request<Api.Monitor.AppHealthData>({
    url: '/monitor/app-health',
    method: 'get'
  });
}

/**
 * 获取业务统计数据
 */
export function fetchBusinessStats() {
  return request<Api.Monitor.BusinessStatsData>({
    url: '/monitor/business-stats',
    method: 'get'
  });
}

/**
 * 获取性能监控数据
 * @param minutes - 统计时间范围（分钟）
 */
export function fetchPerformanceData(minutes: number = 60) {
  return request<Api.Monitor.PerformanceData>({
    url: '/monitor/performance',
    method: 'get',
    params: { minutes }
  });
}

/**
 * 获取系统资源概览
 */
export function fetchSystemOverview() {
  return request<Api.Monitor.SystemOverviewData>({
    url: '/monitor/system-overview',
    method: 'get'
  });
}

/**
 * 获取错误分析数据
 * @param params - 查询参数
 */
export function fetchErrorAnalysis(params?: {
  start_date?: string;
  end_date?: string;
  error_type?: string;
  hours?: number;
}) {
  return request<Api.Monitor.ErrorAnalysisData>({
    url: '/monitor/error-analysis',
    method: 'get',
    params
  });
}

/**
 * 获取API性能趋势数据
 * @param hours - 统计时间范围（小时）
 * @param interval_minutes - 时间间隔（分钟）
 */
export function fetchApiTrends(hours: number = 24, interval_minutes: number = 60) {
  return request<Api.Monitor.ApiTrendsData>({
    url: '/monitor/api-trends',
    method: 'get',
    params: { hours, interval_minutes }
  });
}

/**
 * 获取慢查询列表
 * @param threshold_seconds - 慢查询阈值（秒）
 * @param limit - 返回数量限制
 */
export function fetchSlowQueries(threshold_seconds: number = 2.0, limit: number = 10) {
  return request<Api.Monitor.SlowQueriesData>({
    url: '/monitor/slow-queries',
    method: 'get',
    params: { threshold_seconds, limit }
  });
}

/**
 * 获取STRM任务性能统计
 */
export function fetchTaskPerformanceStats() {
  return request<Api.Monitor.TaskPerformanceData>({
    url: '/monitor/task-performance',
    method: 'get'
  });
}

/**
 * 获取错误分析数据
 * @param hours - 分析时间范围（小时）
 * @param error_type - 错误类型过滤（可选）
 */
export function fetchErrorAnalysis(hours: number = 24, error_type?: string) {
  return request<Api.Monitor.ErrorAnalysisData>({
    url: '/monitor/error-analysis',
    method: 'get',
    params: { hours, error_type }
  });
}

/**
 * 获取STRM任务详细统计
 * @param hours - 统计时间范围（小时）
 */
export function fetchStrmTaskStats(hours: number = 24) {
  return request<Api.Monitor.StrmTaskStatsData>({
    url: '/monitor/strm-task-stats',
    method: 'get',
    params: { hours }
  });
}

/**
 * 清理监控缓存
 * @param pattern - 缓存模式（可选）
 */
export function clearMonitorCache(pattern?: string) {
  return request<Api.Common.CommonResult<{ cleared_count: number }>>({
    url: '/monitor/cache/clear',
    method: 'post',
    data: { pattern }
  });
}

/**
 * 获取缓存信息
 */
export function fetchCacheInfo() {
  return request<Api.Monitor.CacheInfoData>({
    url: '/monitor/cache/info',
    method: 'get'
  });
}

/**
 * 预热监控缓存
 */
export function warmUpCache() {
  return request<Api.Common.CommonResult<Record<string, boolean>>>({
    url: '/monitor/cache/warmup',
    method: 'post'
  });
}
